# 🏆 Fantasy Premier League End of Season Review Dashboard

An interactive web dashboard for analyzing Fantasy Premier League league **623787** with comprehensive statistics, visualizations, and manager analysis perfect for an end-of-season review party!

## 🌟 Features

### 📊 League Overview
- **Champion Details**: Winner's name, team, points, and winning margin
- **League Statistics**: Total managers, average points, highest/lowest scores
- **Final League Table**: Top 10 standings with clean formatting

### 📈 Season Analysis
- **Points Progression**: Interactive line chart showing how each manager accumulated points throughout the season
- **Performance Tracking**: See who had the best runs of form and when
- **Comparative Analysis**: Compare against league averages

### 👤 Individual Manager Analysis
- **Detailed Statistics**: Total points, averages, best/worst gameweeks
- **Transfer Analysis**: Total transfers made and points cost
- **Bench Points**: Points left on the bench throughout the season
- **Performance Charts**: Gameweek-by-gameweek performance with trend lines

## 🚀 Quick Start

### Option 1: Automated Setup
```bash
python run_fpl_dashboard.py
```

### Option 2: Manual Setup
1. **Install Dependencies**:
   ```bash
   pip install -r requirements_fpl.txt
   ```

2. **Run the Dashboard**:
   ```bash
   python fpl_end_of_season_dashboard.py
   ```

3. **Open Your Browser**:
   Navigate to: `http://127.0.0.1:8050`

## 📋 Requirements

- Python 3.7+
- Internet connection (for FPL API access)
- Web browser

### Python Packages
- `requests` - For API calls
- `pandas` - Data manipulation
- `plotly` - Interactive charts
- `dash` - Web dashboard framework
- `numpy` - Numerical operations

## 🎯 Perfect for End of Season Parties

This dashboard is designed specifically for Fantasy Premier League end-of-season review parties:

### 🏆 **League Champion Celebration**
- Highlight the winner with detailed statistics
- Show the winning margin and final points total
- Compare champion's performance to league average

### 📊 **Season Highlights**
- Identify the best and worst gameweeks for each manager
- Track who had the most consistent performance
- Analyze transfer strategies and their effectiveness

### 🎮 **Interactive Exploration**
- Let party guests explore their own statistics
- Compare different managers' performances
- Discover interesting patterns and trends

### 📈 **Data-Driven Insights**
- See who peaked early vs. who finished strong
- Identify the most active transfer managers
- Find out who left the most points on the bench

## 🔧 Technical Details

### Data Sources
- **FPL API**: Official Fantasy Premier League API
- **League Data**: Standings, manager information, points
- **Manager Histories**: Gameweek-by-gameweek performance data
- **Bootstrap Data**: General FPL information and gameweek details

### Dashboard Components
- **Dash Framework**: Interactive web application
- **Plotly Charts**: Professional-quality visualizations
- **Responsive Design**: Works on desktop and mobile devices

### API Rate Limiting
The script includes built-in rate limiting to be respectful to the FPL API:
- 0.5-second delays between requests
- Efficient data caching to minimize API calls
- Error handling for network issues

## 🎨 Dashboard Sections

1. **Header**: League name and basic information
2. **Overview Cards**: Champion details and league statistics
3. **Final League Table**: Top 10 standings
4. **Points Progression Chart**: Season-long performance tracking
5. **Manager Analysis**: Individual deep-dive with dropdown selection

## 🐛 Troubleshooting

### Common Issues

**"Module not found" errors**:
```bash
pip install -r requirements_fpl.txt
```

**Dashboard won't load**:
- Check that port 8050 is available
- Ensure you have an internet connection for API access
- Wait for data collection to complete (may take 1-2 minutes)

**No data showing**:
- Verify league ID 623787 is correct and public
- Check internet connection
- Try refreshing the browser page

### Performance Notes
- Initial data collection takes 1-2 minutes
- Dashboard loads faster on subsequent runs due to caching
- Large leagues (50+ managers) may take longer to load

## 📱 Browser Compatibility

Tested and optimized for:
- Chrome (recommended)
- Firefox
- Safari
- Edge

## 🎉 Enjoy Your End of Season Review!

This dashboard provides everything you need for a comprehensive and entertaining Fantasy Premier League season review. Perfect for settling debates about who really had the best season! 🏆
