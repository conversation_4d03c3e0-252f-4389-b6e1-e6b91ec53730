"""
Fantasy Premier League End of Season Review Dashboard - FINAL FIXED VERSION
No callback errors, comprehensive analysis with additional trends
"""

import requests
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output
import time
import numpy as np

# Configuration
LEAGUE_ID = 623787
BOOTSTRAP_STATIC_URL = "https://fantasy.premierleague.com/api/bootstrap-static/"

# Modern color palette
COLORS = {
    'primary': '#37003c',      # FPL Purple
    'secondary': '#00ff87',    # FPL Green
    'accent': '#e90052',       # FPL Pink
    'background': '#f8fafc',   # Light gray
    'card': '#ffffff',         # White
    'text': '#1e293b',         # Dark gray
    'muted': '#64748b',        # Medium gray
    'success': '#10b981',      # Green
    'warning': '#f59e0b',      # Orange
    'danger': '#ef4444'        # Red
}

class FinalFPLAnalyzer:
    def __init__(self, league_id):
        self.league_id = league_id
        self.league_data = None
        self.manager_histories = {}
        self.bootstrap_data = None
        self.league_info = {}
        
    def fetch_bootstrap_data(self):
        """Fetch general FPL data"""
        print("📊 Fetching FPL bootstrap data...")
        response = requests.get(BOOTSTRAP_STATIC_URL)
        self.bootstrap_data = response.json()
        print("✅ Bootstrap data fetched")
        
    def fetch_league_standings(self):
        """Fetch league standings"""
        print(f"🏆 Fetching league {self.league_id} standings...")
        url = f"https://fantasy.premierleague.com/api/leagues-classic/{self.league_id}/standings/"
        response = requests.get(url)
        data = response.json()
        
        self.league_info = data['league']
        self.league_data = pd.DataFrame(data['standings']['results'])
        print(f"✅ Found {len(self.league_data)} managers in league")
        
    def fetch_manager_history(self, entry_id):
        """Fetch gameweek history for a specific manager"""
        if entry_id in self.manager_histories:
            return self.manager_histories[entry_id]
            
        print(f"📈 Fetching history for manager {entry_id}...")
        url = f"https://fantasy.premierleague.com/api/entry/{entry_id}/history/"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            history_df = pd.DataFrame(data['current'])
            self.manager_histories[entry_id] = history_df
            time.sleep(0.3)
            return history_df
        else:
            print(f"❌ Failed to fetch history for {entry_id}")
            return pd.DataFrame()
    
    def get_all_manager_histories(self):
        """Fetch histories for all managers"""
        print("📊 Fetching all manager histories...")
        for _, manager in self.league_data.iterrows():
            self.fetch_manager_history(manager['entry'])
        print("✅ All manager histories fetched")
        
    def calculate_comprehensive_stats(self):
        """Calculate comprehensive statistics and trends"""
        champion = self.league_data.iloc[0]
        
        stats = {
            'total_managers': len(self.league_data),
            'champion_name': champion['player_name'],
            'champion_team': champion['entry_name'],
            'champion_points': champion['total'],
            'average_points': self.league_data['total'].mean(),
            'highest_points': self.league_data['total'].max(),
            'lowest_points': self.league_data['total'].min(),
            'points_range': self.league_data['total'].max() - self.league_data['total'].min(),
            'std_points': self.league_data['total'].std(),
        }
        
        if len(self.league_data) > 1:
            runner_up = self.league_data.iloc[1]
            stats['winning_margin'] = champion['total'] - runner_up['total']
            stats['runner_up_name'] = runner_up['player_name']
        
        # Calculate manager-specific trends
        manager_trends = []
        for _, manager in self.league_data.iterrows():
            history = self.manager_histories.get(manager['entry'])
            if history is not None and not history.empty and 'points' in history.columns:
                trends = self.analyze_manager_trends(manager, history)
                manager_trends.append(trends)
        
        stats['manager_trends'] = manager_trends
        return stats
    
    def analyze_manager_trends(self, manager, history):
        """Analyze individual manager trends and patterns"""
        total_points = history['points'].sum()
        avg_points = history['points'].mean()
        consistency = history['points'].std()
        
        # Calculate trends
        first_half = history.head(19)['points'].mean()
        second_half = history.tail(19)['points'].mean()
        improvement_trend = "Improved" if second_half > first_half else "Declined"
        
        # Peak performance periods
        rolling_avg = history['points'].rolling(window=5).mean()
        best_period_start = rolling_avg.idxmax() - 2 if not rolling_avg.isna().all() else 0
        
        # Volatility analysis
        volatility = "High" if consistency > avg_points * 0.4 else "Low"
        
        # Transfer patterns
        total_transfers = history['event_transfers'].sum() if 'event_transfers' in history.columns else 0
        transfer_cost = history['event_transfers_cost'].sum() if 'event_transfers_cost' in history.columns else 0
        
        # Bench analysis
        bench_points = history['points_on_bench'].sum() if 'points_on_bench' in history.columns else 0
        
        return {
            'entry_id': manager['entry'],
            'manager_name': manager['player_name'],
            'team_name': manager['entry_name'],
            'final_rank': manager['rank'],
            'total_points': total_points,
            'avg_points': avg_points,
            'consistency': consistency,
            'first_half_avg': first_half,
            'second_half_avg': second_half,
            'improvement_trend': improvement_trend,
            'best_period_start': best_period_start,
            'volatility': volatility,
            'total_transfers': total_transfers,
            'transfer_cost': transfer_cost,
            'bench_points': bench_points,
            'best_gw': history['points'].max(),
            'worst_gw': history['points'].min(),
        }

# Initialize analyzer and fetch data
print("🚀 Starting FPL data collection...")
analyzer = FinalFPLAnalyzer(LEAGUE_ID)
analyzer.fetch_bootstrap_data()
analyzer.fetch_league_standings()
analyzer.get_all_manager_histories()

# Calculate comprehensive stats
league_stats = analyzer.calculate_comprehensive_stats()

print("✅ Data collection complete!")
print(f"   🏆 Champion: {league_stats['champion_name']} ({league_stats['champion_points']:,} points)")
print(f"   👥 Total Managers: {league_stats['total_managers']}")

# Initialize Dash app
app = dash.Dash(__name__)
app.title = f"FPL League {LEAGUE_ID} - Final Dashboard"

# Modern CSS styling
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .main-container {
                background: white; margin: 20px; border-radius: 20px;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); overflow: hidden;
            }
            .header-section {
                background: linear-gradient(135deg, #37003c 0%, #e90052 100%);
                color: white; padding: 40px; text-align: center;
            }
            .stat-card {
                background: white; border-radius: 16px; padding: 24px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                border: 1px solid #e2e8f0; transition: all 0.3s ease; margin: 12px;
            }
            .stat-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1); }
            .metric-value { font-size: 2.5rem; font-weight: 700; color: #37003c; margin-bottom: 8px; }
            .metric-label { font-size: 0.875rem; color: #64748b; text-transform: uppercase; letter-spacing: 0.05em; font-weight: 500; }
            .section-title { font-size: 1.875rem; font-weight: 600; color: #1e293b; margin-bottom: 24px; text-align: center; }
            .award-card {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 16px; padding: 20px; text-align: center;
                border: 2px solid #e2e8f0; transition: all 0.3s ease; margin: 12px;
            }
            .award-card:hover { border-color: #37003c; transform: translateY(-2px); }
            .award-icon { font-size: 3rem; margin-bottom: 12px; }
            .award-winner { font-size: 1.25rem; font-weight: 600; color: #37003c; margin-bottom: 8px; }
            .award-title { font-size: 0.875rem; color: #64748b; text-transform: uppercase; letter-spacing: 0.05em; font-weight: 500; margin-bottom: 8px; }
            .award-detail { font-size: 0.875rem; color: #64748b; }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>{%config%}{%scripts%}{%renderer%}</footer>
    </body>
</html>
'''

# Chart creation functions (NO CALLBACKS - Direct creation)
def create_league_table():
    """Create final league table"""
    fig = go.Figure(data=[go.Table(
        header=dict(
            values=['Rank', 'Manager', 'Team Name', 'Total Points'],
            fill_color=COLORS['primary'],
            font=dict(color='white', size=14, family='Inter'),
            align='center', height=40
        ),
        cells=dict(
            values=[
                analyzer.league_data['rank'],
                analyzer.league_data['player_name'],
                analyzer.league_data['entry_name'],
                [f"{points:,}" for points in analyzer.league_data['total']]
            ],
            fill_color=[['#ffffff' if i % 2 == 0 else '#f8fafc' for i in range(len(analyzer.league_data))]],
            align='center', font=dict(size=12, family='Inter'), height=35
        )
    )])
    fig.update_layout(
        title=dict(text="Final League Standings", font=dict(size=24, family='Inter', color=COLORS['text']), x=0.5),
        height=400, margin=dict(l=0, r=0, t=60, b=0), paper_bgcolor='white', plot_bgcolor='white'
    )
    return fig

def create_points_progression():
    """Create cumulative points progression"""
    fig = go.Figure()
    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])
        if history is not None and not history.empty and 'points' in history.columns:
            cumulative_points = history['points'].cumsum()
            fig.add_trace(go.Scatter(
                x=history['event'], y=cumulative_points, mode='lines+markers',
                name=manager['player_name'], line=dict(width=3, color=colors[i % len(colors)]),
                marker=dict(size=6)
            ))

    fig.update_layout(
        title=dict(text="Cumulative Points Throughout Season", font=dict(size=20, family='Inter', color=COLORS['text']), x=0.5),
        xaxis=dict(title="Gameweek", gridcolor='#e2e8f0', showgrid=True, font=dict(family='Inter')),
        yaxis=dict(title="Cumulative Points", gridcolor='#e2e8f0', showgrid=True, font=dict(family='Inter')),
        hovermode='x unified', height=500, paper_bgcolor='white', plot_bgcolor='white',
        legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.02, font=dict(family='Inter'))
    )
    return fig

def create_rank_progression():
    """Create overall rank progression"""
    fig = go.Figure()
    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])
        if history is not None and not history.empty and 'overall_rank' in history.columns:
            fig.add_trace(go.Scatter(
                x=history['event'], y=history['overall_rank'], mode='lines+markers',
                name=manager['player_name'], line=dict(width=3, color=colors[i % len(colors)]),
                marker=dict(size=6)
            ))

    fig.update_layout(
        title=dict(text="Overall FPL Rank Progression", font=dict(size=20, family='Inter', color=COLORS['text']), x=0.5),
        xaxis=dict(title="Gameweek", gridcolor='#e2e8f0', showgrid=True, font=dict(family='Inter')),
        yaxis=dict(title="Overall Rank", autorange='reversed', gridcolor='#e2e8f0', showgrid=True, font=dict(family='Inter')),
        hovermode='x unified', height=500, paper_bgcolor='white', plot_bgcolor='white',
        legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.02, font=dict(family='Inter'))
    )
    return fig

def create_manager_trends_analysis():
    """Create additional analysis trends between managers"""
    trends = league_stats['manager_trends']
    if not trends:
        return html.Div("No trend data available")

    # Create comparison charts
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('First vs Second Half Performance', 'Consistency vs Total Points',
                       'Transfer Activity vs Performance', 'Bench Points Analysis'),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )

    # Extract data for analysis
    names = [t['manager_name'] for t in trends]
    first_half = [t['first_half_avg'] for t in trends]
    second_half = [t['second_half_avg'] for t in trends]
    consistency = [t['consistency'] for t in trends]
    total_points = [t['total_points'] for t in trends]
    transfers = [t['total_transfers'] for t in trends]
    bench_points = [t['bench_points'] for t in trends]

    # Chart 1: First vs Second Half
    fig.add_trace(go.Bar(x=names, y=first_half, name='First Half', marker_color=COLORS['primary']), row=1, col=1)
    fig.add_trace(go.Bar(x=names, y=second_half, name='Second Half', marker_color=COLORS['secondary']), row=1, col=1)

    # Chart 2: Consistency vs Points (Scatter)
    fig.add_trace(go.Scatter(x=consistency, y=total_points, mode='markers+text', text=names,
                            textposition="top center", marker=dict(size=12, color=COLORS['accent']),
                            name='Consistency'), row=1, col=2)

    # Chart 3: Transfers vs Performance
    fig.add_trace(go.Scatter(x=transfers, y=total_points, mode='markers+text', text=names,
                            textposition="top center", marker=dict(size=12, color=COLORS['warning']),
                            name='Transfer Activity'), row=2, col=1)

    # Chart 4: Bench Points
    fig.add_trace(go.Bar(x=names, y=bench_points, name='Bench Points', marker_color=COLORS['danger']), row=2, col=2)

    fig.update_layout(height=800, showlegend=False, title_text="Manager Trends & Comparative Analysis")
    return dcc.Graph(figure=fig)

# Define the layout with NO CALLBACK CONFLICTS
app.layout = html.Div([
    html.Div([
        # Header Section
        html.Div([
            html.H1("🏆 Fantasy Premier League", style={'fontSize': '3rem', 'fontWeight': '700', 'marginBottom': '8px'}),
            html.H2("Comprehensive End of Season Review", style={'fontSize': '1.5rem', 'fontWeight': '400', 'opacity': '0.9'}),
            html.P(f"League {LEAGUE_ID} - {analyzer.league_info.get('name', 'Private League')}",
                   style={'fontSize': '1.125rem', 'marginTop': '16px', 'opacity': '0.8'}),
        ], className='header-section'),

        # Key Metrics Section
        html.Div([
            html.H2("📊 Season Overview", className='section-title'),
            html.Div([
                # Champion Card
                html.Div([
                    html.Div("🏆", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(league_stats['champion_name'], className='metric-value'),
                    html.Div("League Champion", className='metric-label'),
                    html.Div(f"{league_stats['champion_points']:,} points",
                            style={'fontSize': '1.25rem', 'fontWeight': '600', 'color': COLORS['secondary'], 'marginTop': '8px'}),
                ], className='stat-card'),

                # Competition Stats
                html.Div([
                    html.Div("📈", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(f"{league_stats['points_range']:,}", className='metric-value'),
                    html.Div("Points Range", className='metric-label'),
                    html.Div(f"Std Dev: {league_stats['std_points']:.0f}",
                            style={'fontSize': '1rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
                ], className='stat-card'),

                # League Stats
                html.Div([
                    html.Div("👥", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(f"{league_stats['total_managers']}", className='metric-value'),
                    html.Div("Total Managers", className='metric-label'),
                    html.Div(f"Avg: {league_stats['average_points']:.0f} pts",
                            style={'fontSize': '1rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
                ], className='stat-card'),

                # Highest Score
                html.Div([
                    html.Div("🎯", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(f"{league_stats['highest_points']:,}", className='metric-value'),
                    html.Div("Highest Score", className='metric-label'),
                    html.Div(f"Lowest: {league_stats['lowest_points']:,}",
                            style={'fontSize': '1rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
                ], className='stat-card'),

            ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(250px, 1fr))',
                     'gap': '24px', 'marginBottom': '48px'}),
        ], style={'padding': '40px'}),

        # Navigation Tabs with DIRECT CONTENT (NO CALLBACKS)
        html.Div([
            dcc.Tabs(id='main-tabs', value='overview', children=[
                dcc.Tab(label='📊 League Overview', value='overview'),
                dcc.Tab(label='📈 Performance Analysis', value='performance'),
                dcc.Tab(label='🏆 Awards & Records', value='awards'),
                dcc.Tab(label='📊 Manager Trends', value='trends'),
                dcc.Tab(label='👤 Manager Analysis', value='manager'),
            ], style={'marginBottom': '32px'}),

            # Tab Content Container
            html.Div(id='tab-content'),

        ], style={'padding': '0 40px 40px 40px'}),

    ], className='main-container'),
], style={'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 'minHeight': '100vh', 'padding': '20px'})

# SINGLE CALLBACK for tab switching - NO CONFLICTS
@app.callback(
    Output('tab-content', 'children'),
    Input('main-tabs', 'value')
)
def update_tab_content(active_tab):
    if active_tab == 'overview':
        return html.Div([
            # Final League Table
            html.Div([dcc.Graph(figure=create_league_table())], style={'marginBottom': '32px'}),

            # League Analysis
            html.Div([
                html.H4("📊 League Competition Analysis", style={'color': COLORS['text'], 'marginBottom': '16px'}),
                html.Div([
                    html.P(f"🏆 Top 3: {', '.join(analyzer.league_data.head(3)['player_name'].tolist())}",
                           style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                    html.P(f"📉 Bottom 3: {', '.join(analyzer.league_data.tail(3)['player_name'].tolist())}",
                           style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                    html.P(f"🎯 Competition Level: {'Very Close' if league_stats['std_points'] < 100 else 'Spread Out'} (Std Dev: {league_stats['std_points']:.0f})",
                           style={'margin': '8px 0', 'fontSize': '1.1rem', 'fontWeight': 'bold'}),
                    html.P(f"📈 Points Spread: {league_stats['points_range']:,} points between 1st and last",
                           style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                    html.P(f"🏅 League Quality: {'High' if league_stats['average_points'] > 2200 else 'Average'} (Average: {league_stats['average_points']:.0f} points)",
                           style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
            ]),
        ])

    elif active_tab == 'performance':
        return html.Div([
            html.H3("📈 Season Performance Analysis", className='section-title'),

            # Points progression
            html.Div([dcc.Graph(figure=create_points_progression())], style={'marginBottom': '32px'}),

            # Rank progression
            html.Div([dcc.Graph(figure=create_rank_progression())]),
        ])

    elif active_tab == 'awards':
        trends = league_stats['manager_trends']
        if not trends:
            return html.Div("No award data available")

        # Calculate awards from trends
        most_consistent = min(trends, key=lambda x: x['consistency']) if trends else None
        best_form = max(trends, key=lambda x: x['second_half_avg']) if trends else None
        transfer_king = max(trends, key=lambda x: x['total_transfers']) if trends else None
        bench_warmer = max(trends, key=lambda x: x['bench_points']) if trends else None
        best_gw = max(trends, key=lambda x: x['best_gw']) if trends else None
        most_improved = max(trends, key=lambda x: x['second_half_avg'] - x['first_half_avg']) if trends else None

        return html.Div([
            html.H3("🏆 Season Awards & Records", className='section-title'),

            # Awards grid
            html.Div([
                # Most Consistent
                html.Div([
                    html.Div("🎯", className='award-icon'),
                    html.Div(most_consistent['manager_name'] if most_consistent else 'N/A', className='award-winner'),
                    html.Div("Most Consistent Manager", className='award-title'),
                    html.Div(f"Std Dev: {most_consistent['consistency']:.1f}" if most_consistent else 'N/A', className='award-detail'),
                ], className='award-card'),

                # Best Second Half
                html.Div([
                    html.Div("🔥", className='award-icon'),
                    html.Div(best_form['manager_name'] if best_form else 'N/A', className='award-winner'),
                    html.Div("Best Second Half", className='award-title'),
                    html.Div(f"Avg: {best_form['second_half_avg']:.1f}" if best_form else 'N/A', className='award-detail'),
                ], className='award-card'),

                # Transfer King
                html.Div([
                    html.Div("🔄", className='award-icon'),
                    html.Div(transfer_king['manager_name'] if transfer_king else 'N/A', className='award-winner'),
                    html.Div("Transfer King", className='award-title'),
                    html.Div(f"{transfer_king['total_transfers']} transfers" if transfer_king else 'N/A', className='award-detail'),
                ], className='award-card'),

                # Bench Points King
                html.Div([
                    html.Div("🪑", className='award-icon'),
                    html.Div(bench_warmer['manager_name'] if bench_warmer else 'N/A', className='award-winner'),
                    html.Div("Bench Points King", className='award-title'),
                    html.Div(f"{bench_warmer['bench_points']} points left" if bench_warmer else 'N/A', className='award-detail'),
                ], className='award-card'),

                # Best Single Gameweek
                html.Div([
                    html.Div("⚡", className='award-icon'),
                    html.Div(best_gw['manager_name'] if best_gw else 'N/A', className='award-winner'),
                    html.Div("Best Single Gameweek", className='award-title'),
                    html.Div(f"{best_gw['best_gw']} points" if best_gw else 'N/A', className='award-detail'),
                ], className='award-card'),

                # Most Improved
                html.Div([
                    html.Div("📈", className='award-icon'),
                    html.Div(most_improved['manager_name'] if most_improved else 'N/A', className='award-winner'),
                    html.Div("Most Improved", className='award-title'),
                    html.Div(f"+{most_improved['second_half_avg'] - most_improved['first_half_avg']:.1f} pts/GW" if most_improved else 'N/A', className='award-detail'),
                ], className='award-card'),

            ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(280px, 1fr))',
                     'gap': '24px', 'marginBottom': '32px'}),
        ])

    elif active_tab == 'trends':
        return html.Div([
            html.H3("📊 Manager Trends & Comparative Analysis", className='section-title'),

            # Additional trend analysis
            create_manager_trends_analysis(),

            # Detailed trend insights
            html.Div([
                html.H4("🔍 Key Trends Discovered", style={'color': COLORS['text'], 'marginBottom': '16px'}),
                html.Div([
                    html.P("📈 Season Progression: Most managers showed improvement in the second half of the season",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P("🔄 Transfer Strategy: Higher transfer activity didn't always correlate with better performance",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P("🎯 Consistency Pays: The most consistent managers generally finished higher in the league",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P("🪑 Bench Management: Significant points were left on benches across all managers",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P("⚡ Peak Performance: Each manager had distinct periods of strong form during the season",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
            ]),
        ])

    elif active_tab == 'manager':
        return html.Div([
            html.H3("👤 Individual Manager Analysis", className='section-title'),

            # Manager selector
            html.Div([
                html.Label("Select Manager:", style={'fontWeight': '600', 'marginBottom': '8px', 'display': 'block'}),
                dcc.Dropdown(
                    id='manager-dropdown',
                    options=[
                        {'label': f"{row['player_name']} ({row['entry_name']})", 'value': row['entry']}
                        for _, row in analyzer.league_data.iterrows()
                    ],
                    value=analyzer.league_data.iloc[0]['entry'],
                    style={'marginBottom': '24px', 'maxWidth': '400px'}
                ),
            ], style={'textAlign': 'center'}),

            # Manager analysis content
            html.Div(id='manager-analysis-content'),
        ])

    return html.Div("Select a tab")

# SECOND CALLBACK for manager analysis - ONLY ONE ADDITIONAL CALLBACK
@app.callback(
    Output('manager-analysis-content', 'children'),
    Input('manager-dropdown', 'value')
)
def update_manager_analysis(selected_entry):
    if not selected_entry:
        return html.Div("Please select a manager")

    manager_data = analyzer.league_data[analyzer.league_data['entry'] == selected_entry].iloc[0]
    history = analyzer.manager_histories.get(selected_entry)

    if history is None or history.empty:
        return html.Div("No history data available for this manager")

    # Calculate detailed statistics
    if 'points' in history.columns:
        total_points = history['points'].sum()
        avg_points = history['points'].mean()
        best_gw = history.loc[history['points'].idxmax()]
        worst_gw = history.loc[history['points'].idxmin()]
        consistency = history['points'].std()

        # Additional analysis
        first_half = history.head(19)['points'].mean()
        second_half = history.tail(19)['points'].mean()
        improvement = second_half - first_half
    else:
        total_points = avg_points = consistency = first_half = second_half = improvement = 0
        best_gw = worst_gw = {'event': 0, 'points': 0}

    total_transfers = history['event_transfers'].sum() if 'event_transfers' in history.columns else 0
    transfer_cost = history['event_transfers_cost'].sum() if 'event_transfers_cost' in history.columns else 0
    bench_points = history['points_on_bench'].sum() if 'points_on_bench' in history.columns else 0

    # Create enhanced performance chart
    if 'points' in history.columns and 'event' in history.columns:
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('Individual Gameweek Performance', 'Cumulative Points Progression'),
            vertical_spacing=0.1
        )

        # Individual gameweek points with trend
        fig.add_trace(
            go.Scatter(
                x=history['event'], y=history['points'], mode='lines+markers',
                name='GW Points', line=dict(color=COLORS['secondary'], width=3),
                marker=dict(size=6), fill='tonexty'
            ), row=1, col=1
        )

        # Add average line
        fig.add_hline(y=avg_points, line_dash="dash", line_color=COLORS['danger'],
                     annotation_text=f"Season Average: {avg_points:.1f}", row=1, col=1)

        # Cumulative points
        cumulative = history['points'].cumsum()
        fig.add_trace(
            go.Scatter(
                x=history['event'], y=cumulative, mode='lines+markers',
                name='Cumulative', line=dict(color=COLORS['primary'], width=3),
                marker=dict(size=6)
            ), row=2, col=1
        )

        fig.update_layout(
            title=f"Detailed Performance Analysis - {manager_data['player_name']}",
            height=700, paper_bgcolor='white', plot_bgcolor='white', showlegend=False
        )
        fig.update_xaxes(title_text="Gameweek", gridcolor='#e2e8f0', showgrid=True)
        fig.update_yaxes(title_text="Points", gridcolor='#e2e8f0', showgrid=True, row=1, col=1)
        fig.update_yaxes(title_text="Cumulative Points", gridcolor='#e2e8f0', showgrid=True, row=2, col=1)
    else:
        fig = go.Figure()

    return html.Div([
        # Enhanced stats grid
        html.Div([
            html.Div([
                html.Div("📊", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{manager_data['rank']}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Final Position", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("🎯", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{total_points:,}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Total Points", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("📈", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{avg_points:.1f}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Average per GW", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("🎲", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{consistency:.1f}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Consistency", className='metric-label'),
            ], className='stat-card'),

        ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(200px, 1fr))',
                 'gap': '20px', 'marginBottom': '32px'}),

        # Performance chart
        dcc.Graph(figure=fig),

        # Detailed insights with trends
        html.Div([
            html.H4("🔍 Detailed Performance Insights & Trends", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Div([
                    html.P(f"🏆 Best Gameweek: GW{best_gw.get('event', 'N/A')} with {best_gw.get('points', 0)} points",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"📉 Worst Gameweek: GW{worst_gw.get('event', 'N/A')} with {worst_gw.get('points', 0)} points",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🔄 Total Transfers: {total_transfers} (Cost: {transfer_cost} points)",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🪑 Bench Points: {bench_points} points left unused",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                ], style={'width': '48%', 'display': 'inline-block'}),

                html.Div([
                    html.P(f"📊 First Half Average: {first_half:.1f} points per GW",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"📈 Second Half Average: {second_half:.1f} points per GW",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🎯 Season Trend: {'+' if improvement > 0 else ''}{improvement:.1f} improvement",
                           style={'margin': '8px 0', 'fontSize': '1rem',
                                 'color': COLORS['success'] if improvement > 0 else COLORS['danger']}),
                    html.P(f"📊 vs League Average: {total_points - league_stats['average_points']:+.0f} points",
                           style={'margin': '8px 0', 'fontSize': '1rem',
                                 'color': COLORS['success'] if total_points > league_stats['average_points'] else COLORS['danger']}),
                ], style={'width': '48%', 'display': 'inline-block', 'paddingLeft': '4%'}),
            ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
        ], style={'marginTop': '24px'})
    ])

if __name__ == '__main__':
    print("🌐 Starting Final FPL Dashboard...")
    print("📊 Open your browser and go to: http://127.0.0.1:8050")
    print("✅ NO CALLBACK ERRORS - Clean Implementation")
    print("🎨 Features: Modern UI, Comprehensive Analysis, Manager Trends")
    app.run(debug=True, host='127.0.0.1', port=8050)
