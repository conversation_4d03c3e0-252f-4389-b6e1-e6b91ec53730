"""
Fantasy Premier League End of Season Review Dashboard - FIXED VERSION
Clean implementation with working callbacks and data display
"""

import requests
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output
import time

# Configuration
LEAGUE_ID = 623787
BOOTSTRAP_STATIC_URL = "https://fantasy.premierleague.com/api/bootstrap-static/"

# Modern color palette
COLORS = {
    'primary': '#37003c',      # FPL Purple
    'secondary': '#00ff87',    # FPL Green
    'accent': '#e90052',       # FPL Pink
    'background': '#f8fafc',   # Light gray
    'card': '#ffffff',         # White
    'text': '#1e293b',         # Dark gray
    'muted': '#64748b',        # Medium gray
    'success': '#10b981',      # Green
    'warning': '#f59e0b',      # Orange
    'danger': '#ef4444'        # Red
}

class FPLAnalyzer:
    def __init__(self, league_id):
        self.league_id = league_id
        self.league_data = None
        self.gameweek_data = None
        self.manager_histories = {}
        self.bootstrap_data = None
        self.league_info = {}
        
    def fetch_bootstrap_data(self):
        """Fetch general FPL data including gameweeks"""
        print("📊 Fetching FPL bootstrap data...")
        response = requests.get(BOOTSTRAP_STATIC_URL)
        self.bootstrap_data = response.json()
        
        events = self.bootstrap_data['events']
        self.gameweek_data = pd.DataFrame([{
            'id': event['id'],
            'name': event['name'],
            'average_score': event['average_entry_score'],
            'highest_score': event['highest_score'],
            'finished': event['finished'],
            'deadline_time': event['deadline_time']
        } for event in events if event['finished']])
        
        print(f"✅ Found {len(self.gameweek_data)} completed gameweeks")
        
    def fetch_league_standings(self):
        """Fetch league standings with pagination"""
        print(f"🏆 Fetching league {self.league_id} standings...")
        all_results = []
        page = 1
        
        while True:
            url = f"https://fantasy.premierleague.com/api/leagues-classic/{self.league_id}/standings/?page_standings={page}"
            response = requests.get(url)
            data = response.json()
            
            if page == 1:
                self.league_info = data['league']
                
            results = data['standings']['results']
            all_results.extend(results)
            
            if data['standings']['has_next']:
                page += 1
                time.sleep(0.5)
            else:
                break
                
        self.league_data = pd.DataFrame(all_results)
        print(f"✅ Found {len(self.league_data)} managers in league")
        
    def fetch_manager_history(self, entry_id):
        """Fetch gameweek history for a specific manager"""
        if entry_id in self.manager_histories:
            return self.manager_histories[entry_id]
            
        print(f"📈 Fetching history for manager {entry_id}...")
        url = f"https://fantasy.premierleague.com/api/entry/{entry_id}/history/"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            history_df = pd.DataFrame(data['current'])
            self.manager_histories[entry_id] = history_df
            time.sleep(0.3)
            return history_df
        else:
            print(f"❌ Failed to fetch history for {entry_id}")
            return pd.DataFrame()
    
    def get_all_manager_histories(self):
        """Fetch histories for all managers in the league"""
        print("📊 Fetching all manager histories...")
        for _, manager in self.league_data.iterrows():
            self.fetch_manager_history(manager['entry'])
        print("✅ All manager histories fetched")
        
    def calculate_league_stats(self):
        """Calculate comprehensive league statistics"""
        if self.league_data is None:
            return {}
            
        champion = self.league_data.iloc[0]
        runner_up = self.league_data.iloc[1] if len(self.league_data) > 1 else None
        
        stats = {
            'total_managers': len(self.league_data),
            'champion_name': champion['player_name'],
            'champion_team': champion['entry_name'],
            'champion_points': champion['total'],
            'average_points': self.league_data['total'].mean(),
            'highest_points': self.league_data['total'].max(),
            'lowest_points': self.league_data['total'].min(),
            'points_range': self.league_data['total'].max() - self.league_data['total'].min(),
        }
        
        if runner_up is not None:
            stats['winning_margin'] = champion['total'] - runner_up['total']
            stats['runner_up_name'] = runner_up['player_name']
            
        return stats

# Initialize the analyzer
print("🚀 Starting FPL data collection...")
analyzer = FPLAnalyzer(LEAGUE_ID)
analyzer.fetch_bootstrap_data()
analyzer.fetch_league_standings()
analyzer.get_all_manager_histories()

# Calculate stats
league_stats = analyzer.calculate_league_stats()

print("✅ Data collection complete!")
print(f"   🏆 Champion: {league_stats['champion_name']} ({league_stats['champion_points']:,} points)")
print(f"   👥 Total Managers: {league_stats['total_managers']}")
print(f"   📈 Average Points: {league_stats['average_points']:.0f}")

# Initialize Dash app
app = dash.Dash(__name__)
app.title = f"FPL League {LEAGUE_ID} - End of Season Review"

# Chart creation functions
def create_league_table():
    """Create final league table"""
    fig = go.Figure(data=[go.Table(
        header=dict(
            values=['Rank', 'Manager', 'Team Name', 'Total Points'],
            fill_color=COLORS['primary'],
            font=dict(color='white', size=14),
            align='center',
            height=40
        ),
        cells=dict(
            values=[
                analyzer.league_data['rank'],
                analyzer.league_data['player_name'],
                analyzer.league_data['entry_name'],
                [f"{points:,}" for points in analyzer.league_data['total']]
            ],
            fill_color=[['#ffffff' if i % 2 == 0 else '#f8fafc' for i in range(len(analyzer.league_data))]],
            align='center',
            font=dict(size=12),
            height=35
        )
    )])

    fig.update_layout(
        title=dict(
            text="Final League Standings",
            font=dict(size=20, color=COLORS['text']),
            x=0.5
        ),
        height=400,
        margin=dict(l=0, r=0, t=60, b=0),
        paper_bgcolor='white',
        plot_bgcolor='white'
    )
    return fig

def create_points_distribution():
    """Create points distribution histogram"""
    fig = go.Figure()

    fig.add_trace(go.Histogram(
        x=analyzer.league_data['total'],
        nbinsx=max(4, len(analyzer.league_data)),
        marker_color=COLORS['secondary'],
        opacity=0.7,
        name='Points Distribution'
    ))

    avg_points = analyzer.league_data['total'].mean()
    fig.add_vline(
        x=avg_points,
        line_dash="dash",
        line_color=COLORS['danger'],
        annotation_text=f"Average: {avg_points:.0f}"
    )

    fig.update_layout(
        title=dict(
            text="Final Points Distribution",
            font=dict(size=20, color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(title="Total Points"),
        yaxis=dict(title="Number of Managers"),
        height=400,
        paper_bgcolor='white',
        plot_bgcolor='white',
        showlegend=False
    )
    return fig

def create_points_progression():
    """Create cumulative points progression chart"""
    fig = go.Figure()
    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])

        if history is not None and not history.empty:
            if 'points' in history.columns and 'event' in history.columns:
                cumulative_points = history['points'].cumsum()

                fig.add_trace(go.Scatter(
                    x=history['event'],
                    y=cumulative_points,
                    mode='lines+markers',
                    name=manager['player_name'],
                    line=dict(width=3, color=colors[i % len(colors)]),
                    marker=dict(size=6),
                    hovertemplate=f"<b>{manager['player_name']}</b><br>" +
                                 "Gameweek: %{x}<br>" +
                                 "Total Points: %{y:,}<br>" +
                                 "<extra></extra>"
                ))

    fig.update_layout(
        title=dict(
            text="Cumulative Points Throughout Season",
            font=dict(size=20, color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(title="Gameweek", gridcolor='#e2e8f0', showgrid=True),
        yaxis=dict(title="Cumulative Points", gridcolor='#e2e8f0', showgrid=True),
        hovermode='x unified',
        height=500,
        paper_bgcolor='white',
        plot_bgcolor='white',
        legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.02)
    )
    return fig

def create_rank_progression():
    """Create overall rank progression chart"""
    fig = go.Figure()
    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])
        if history is not None and not history.empty and 'overall_rank' in history.columns:
            fig.add_trace(go.Scatter(
                x=history['event'],
                y=history['overall_rank'],
                mode='lines+markers',
                name=manager['player_name'],
                line=dict(width=3, color=colors[i % len(colors)]),
                marker=dict(size=6),
                hovertemplate=f"<b>{manager['player_name']}</b><br>" +
                             "Gameweek: %{x}<br>" +
                             "Overall Rank: %{y:,}<br>" +
                             "<extra></extra>"
            ))

    fig.update_layout(
        title=dict(
            text="Overall Rank Progression",
            font=dict(size=20, color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(title="Gameweek", gridcolor='#e2e8f0', showgrid=True),
        yaxis=dict(title="Overall Rank", autorange='reversed', gridcolor='#e2e8f0', showgrid=True),
        hovermode='x unified',
        height=500,
        paper_bgcolor='white',
        plot_bgcolor='white',
        legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.02)
    )
    return fig

# Define the layout
app.layout = html.Div([
    # Header
    html.Div([
        html.H1("🏆 Fantasy Premier League End of Season Review",
                style={'textAlign': 'center', 'color': 'white', 'fontSize': '2.5rem', 'marginBottom': '10px'}),
        html.H2(f"League {LEAGUE_ID} - {analyzer.league_info.get('name', 'Private League')}",
                style={'textAlign': 'center', 'color': 'white', 'fontSize': '1.5rem', 'opacity': '0.9'}),
    ], style={'background': f'linear-gradient(135deg, {COLORS["primary"]} 0%, {COLORS["accent"]} 100%)',
              'padding': '40px', 'marginBottom': '30px'}),

    # Key Metrics
    html.Div([
        html.Div([
            html.H3("🥇 Champion", style={'color': COLORS['primary'], 'textAlign': 'center'}),
            html.H4(league_stats['champion_name'], style={'margin': '10px 0', 'textAlign': 'center'}),
            html.P(f"Team: {league_stats['champion_team']}", style={'textAlign': 'center'}),
            html.P(f"Points: {league_stats['champion_points']:,}",
                   style={'fontSize': '1.5rem', 'fontWeight': 'bold', 'color': COLORS['secondary'], 'textAlign': 'center'}),
        ], style={'backgroundColor': 'white', 'padding': '20px', 'borderRadius': '10px', 'margin': '10px',
                 'boxShadow': '0 4px 6px rgba(0, 0, 0, 0.1)', 'flex': '1'}),

        html.Div([
            html.H3("📊 League Stats", style={'color': COLORS['primary'], 'textAlign': 'center'}),
            html.P(f"Total Managers: {league_stats['total_managers']}", style={'margin': '5px 0', 'textAlign': 'center'}),
            html.P(f"Average Points: {league_stats['average_points']:.0f}", style={'margin': '5px 0', 'textAlign': 'center'}),
            html.P(f"Points Range: {league_stats['points_range']:,}", style={'margin': '5px 0', 'textAlign': 'center'}),
        ], style={'backgroundColor': 'white', 'padding': '20px', 'borderRadius': '10px', 'margin': '10px',
                 'boxShadow': '0 4px 6px rgba(0, 0, 0, 0.1)', 'flex': '1'}),

    ], style={'display': 'flex', 'justifyContent': 'space-around', 'marginBottom': '30px', 'padding': '0 20px'}),

    # Navigation Tabs
    html.Div([
        dcc.Tabs(id='main-tabs', value='overview', children=[
            dcc.Tab(label='📊 League Overview', value='overview'),
            dcc.Tab(label='📈 Performance Analysis', value='performance'),
            dcc.Tab(label='👤 Manager Analysis', value='manager'),
        ], style={'marginBottom': '20px'}),

        html.Div(id='tab-content'),
    ], style={'padding': '0 20px'}),
])

# Tab content callback
@app.callback(
    Output('tab-content', 'children'),
    Input('main-tabs', 'value')
)
def update_tab_content(active_tab):
    if active_tab == 'overview':
        return html.Div([
            html.H3("🏆 Final League Table", style={'textAlign': 'center', 'color': COLORS['text']}),
            dcc.Graph(figure=create_league_table(), style={'marginBottom': '30px'}),

            html.H3("📊 Points Distribution", style={'textAlign': 'center', 'color': COLORS['text']}),
            dcc.Graph(figure=create_points_distribution()),
        ])

    elif active_tab == 'performance':
        return html.Div([
            html.H3("📈 Season Performance Analysis", style={'textAlign': 'center', 'color': COLORS['text']}),

            dcc.Graph(figure=create_points_progression(), style={'marginBottom': '30px'}),
            dcc.Graph(figure=create_rank_progression()),
        ])

    elif active_tab == 'manager':
        return html.Div([
            html.H3("👤 Individual Manager Analysis", style={'textAlign': 'center', 'color': COLORS['text']}),

            html.Div([
                html.Label("Select Manager:", style={'fontWeight': 'bold', 'marginBottom': '10px'}),
                dcc.Dropdown(
                    id='manager-dropdown',
                    options=[
                        {'label': f"{row['player_name']} ({row['entry_name']})", 'value': row['entry']}
                        for _, row in analyzer.league_data.iterrows()
                    ],
                    value=analyzer.league_data.iloc[0]['entry'],
                    style={'marginBottom': '20px'}
                ),
            ], style={'maxWidth': '400px', 'margin': '0 auto'}),

            html.Div(id='manager-analysis-content'),
        ])

    return html.Div("Select a tab")

# Manager analysis callback
@app.callback(
    Output('manager-analysis-content', 'children'),
    Input('manager-dropdown', 'value')
)
def update_manager_analysis(selected_entry):
    if not selected_entry:
        return html.Div("Please select a manager")

    manager_data = analyzer.league_data[analyzer.league_data['entry'] == selected_entry].iloc[0]
    history = analyzer.manager_histories.get(selected_entry)

    if history is None or history.empty:
        return html.Div("No history data available for this manager")

    # Calculate statistics
    if 'points' in history.columns:
        total_points = history['points'].sum()
        avg_points = history['points'].mean()
        best_gw = history.loc[history['points'].idxmax()]
        worst_gw = history.loc[history['points'].idxmin()]
    else:
        total_points = avg_points = 0
        best_gw = worst_gw = {'event': 0, 'points': 0}

    total_transfers = history['event_transfers'].sum() if 'event_transfers' in history.columns else 0
    transfer_cost = history['event_transfers_cost'].sum() if 'event_transfers_cost' in history.columns else 0
    bench_points = history['points_on_bench'].sum() if 'points_on_bench' in history.columns else 0

    # Create performance chart
    if 'points' in history.columns and 'event' in history.columns:
        fig = make_subplots(rows=2, cols=1, subplot_titles=('Gameweek Points', 'Cumulative Points'))

        # Individual gameweek points
        fig.add_trace(
            go.Scatter(x=history['event'], y=history['points'], mode='lines+markers',
                      name='GW Points', line=dict(color=COLORS['secondary'], width=3)),
            row=1, col=1
        )

        # Add average line
        fig.add_hline(y=avg_points, line_dash="dash", line_color=COLORS['danger'],
                     annotation_text=f"Average: {avg_points:.1f}", row=1, col=1)

        # Cumulative points
        cumulative = history['points'].cumsum()
        fig.add_trace(
            go.Scatter(x=history['event'], y=cumulative, mode='lines+markers',
                      name='Cumulative', line=dict(color=COLORS['primary'], width=3)),
            row=2, col=1
        )

        fig.update_layout(
            title=f"Performance Analysis - {manager_data['player_name']}",
            height=600, paper_bgcolor='white', plot_bgcolor='white', showlegend=False
        )
        fig.update_xaxes(title_text="Gameweek", gridcolor='#e2e8f0', showgrid=True)
        fig.update_yaxes(title_text="Points", gridcolor='#e2e8f0', showgrid=True, row=1, col=1)
        fig.update_yaxes(title_text="Cumulative Points", gridcolor='#e2e8f0', showgrid=True, row=2, col=1)
    else:
        fig = go.Figure()

    return html.Div([
        # Stats cards
        html.Div([
            html.Div([
                html.H4("📊 Season Summary", style={'color': COLORS['primary']}),
                html.P(f"Final Position: {manager_data['rank']}", style={'margin': '5px 0'}),
                html.P(f"Total Points: {total_points:,}", style={'margin': '5px 0', 'fontWeight': 'bold'}),
                html.P(f"Average per GW: {avg_points:.1f}", style={'margin': '5px 0'}),
            ], style={'backgroundColor': 'white', 'padding': '20px', 'borderRadius': '10px', 'margin': '10px',
                     'boxShadow': '0 4px 6px rgba(0, 0, 0, 0.1)', 'flex': '1'}),

            html.Div([
                html.H4("🎯 Performance Highlights", style={'color': COLORS['primary']}),
                html.P(f"Best GW: {best_gw.get('event', 'N/A')} ({best_gw.get('points', 0)} pts)",
                       style={'margin': '5px 0', 'color': COLORS['success']}),
                html.P(f"Worst GW: {worst_gw.get('event', 'N/A')} ({worst_gw.get('points', 0)} pts)",
                       style={'margin': '5px 0', 'color': COLORS['danger']}),
                html.P(f"Total Transfers: {total_transfers}", style={'margin': '5px 0'}),
                html.P(f"Bench Points: {bench_points}", style={'margin': '5px 0'}),
            ], style={'backgroundColor': 'white', 'padding': '20px', 'borderRadius': '10px', 'margin': '10px',
                     'boxShadow': '0 4px 6px rgba(0, 0, 0, 0.1)', 'flex': '1'}),
        ], style={'display': 'flex', 'marginBottom': '20px'}),

        # Performance chart
        dcc.Graph(figure=fig),
    ])

if __name__ == '__main__':
    print("🌐 Starting FPL Dashboard...")
    print("📊 Open your browser and go to: http://127.0.0.1:8050")
    app.run(debug=True, host='127.0.0.1', port=8050)
