"""
Quick setup and run script for the FPL End of Season Dashboard
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_fpl.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def run_dashboard():
    """Run the FPL dashboard"""
    print("🚀 Starting FPL End of Season Dashboard...")
    print("📊 This will fetch data for league 623787 and create an interactive web dashboard")
    print("⏳ Please wait while we collect data from the FPL API...")
    
    try:
        # Import and run the dashboard
        import fpl_end_of_season_dashboard
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all requirements are installed")
        return False
    except Exception as e:
        print(f"❌ Error running dashboard: {e}")
        return False

if __name__ == "__main__":
    print("🏆 FPL End of Season Review Dashboard Setup")
    print("=" * 50)
    
    # Check if requirements file exists
    if not os.path.exists("requirements_fpl.txt"):
        print("❌ requirements_fpl.txt not found!")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Run the dashboard
    print("\n" + "=" * 50)
    run_dashboard()
