"""
Fantasy Premier League End of Season Review Dashboard
Modern, comprehensive analysis dashboard for league 623787
"""

import requests
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output, callback
import time
from datetime import datetime
import numpy as np

# Configuration
LEAGUE_ID = 623787
BOOTSTRAP_STATIC_URL = "https://fantasy.premierleague.com/api/bootstrap-static/"

# Modern color palette
COLORS = {
    'primary': '#37003c',      # FPL Purple
    'secondary': '#00ff87',    # FPL Green
    'accent': '#e90052',       # FPL Pink
    'background': '#f8fafc',   # Light gray
    'card': '#ffffff',         # White
    'text': '#1e293b',         # Dark gray
    'muted': '#64748b',        # Medium gray
    'success': '#10b981',      # Green
    'warning': '#f59e0b',      # Orange
    'danger': '#ef4444'        # Red
}

class FPLAnalyzer:
    def __init__(self, league_id):
        self.league_id = league_id
        self.league_data = None
        self.gameweek_data = None
        self.manager_histories = {}
        self.bootstrap_data = None
        
    def fetch_bootstrap_data(self):
        """Fetch general FPL data including gameweeks"""
        print("📊 Fetching FPL bootstrap data...")
        response = requests.get(BOOTSTRAP_STATIC_URL)
        self.bootstrap_data = response.json()
        
        # Extract gameweek information
        events = self.bootstrap_data['events']
        self.gameweek_data = pd.DataFrame([{
            'id': event['id'],
            'name': event['name'],
            'average_score': event['average_entry_score'],
            'highest_score': event['highest_score'],
            'finished': event['finished'],
            'deadline_time': event['deadline_time']
        } for event in events if event['finished']])
        
        print(f"✅ Found {len(self.gameweek_data)} completed gameweeks")
        
    def fetch_league_standings(self):
        """Fetch league standings with pagination"""
        print(f"🏆 Fetching league {self.league_id} standings...")
        all_results = []
        page = 1
        
        while True:
            url = f"https://fantasy.premierleague.com/api/leagues-classic/{self.league_id}/standings/?page_standings={page}"
            response = requests.get(url)
            data = response.json()
            
            # Store league info from first page
            if page == 1:
                self.league_info = data['league']
                
            results = data['standings']['results']
            all_results.extend(results)
            
            if data['standings']['has_next']:
                page += 1
                time.sleep(0.5)  # Be respectful to the API
            else:
                break
                
        self.league_data = pd.DataFrame(all_results)
        print(f"✅ Found {len(self.league_data)} managers in league")
        
    def fetch_manager_history(self, entry_id):
        """Fetch gameweek history for a specific manager"""
        if entry_id in self.manager_histories:
            return self.manager_histories[entry_id]

        print(f"📈 Fetching history for manager {entry_id}...")
        url = f"https://fantasy.premierleague.com/api/entry/{entry_id}/history/"
        response = requests.get(url)

        if response.status_code == 200:
            data = response.json()
            history_df = pd.DataFrame(data['current'])



            self.manager_histories[entry_id] = history_df
            time.sleep(0.3)  # Rate limiting
            return history_df
        else:
            print(f"❌ Failed to fetch history for {entry_id}")
            return pd.DataFrame()
    
    def get_all_manager_histories(self):
        """Fetch histories for all managers in the league"""
        print("📊 Fetching all manager histories...")
        for _, manager in self.league_data.iterrows():
            self.fetch_manager_history(manager['entry'])
        print("✅ All manager histories fetched")
        
    def calculate_league_stats(self):
        """Calculate comprehensive league statistics"""
        if self.league_data is None:
            return {}

        champion = self.league_data.iloc[0]
        runner_up = self.league_data.iloc[1] if len(self.league_data) > 1 else None

        stats = {
            'total_managers': len(self.league_data),
            'champion_name': champion['player_name'],
            'champion_team': champion['entry_name'],
            'champion_points': champion['total'],
            'average_points': self.league_data['total'].mean(),
            'highest_points': self.league_data['total'].max(),
            'lowest_points': self.league_data['total'].min(),
            'points_range': self.league_data['total'].max() - self.league_data['total'].min(),
        }

        if runner_up is not None:
            stats['winning_margin'] = champion['total'] - runner_up['total']
            stats['runner_up_name'] = runner_up['player_name']

        return stats

    def calculate_advanced_stats(self):
        """Calculate advanced analytics for the league"""
        if not self.manager_histories:
            return pd.DataFrame()

        all_stats = []

        for entry_id, history in self.manager_histories.items():
            if history.empty:
                continue

            manager_data = self.league_data[self.league_data['entry'] == entry_id].iloc[0]

            # Check what columns are actually available
            available_cols = history.columns.tolist()

            # Use correct column names based on FPL API
            points_col = 'points' if 'points' in available_cols else 'total_points'
            transfers_col = 'event_transfers' if 'event_transfers' in available_cols else 'transfers'
            transfer_cost_col = 'event_transfers_cost' if 'event_transfers_cost' in available_cols else 'transfer_cost'
            bench_col = 'points_on_bench' if 'points_on_bench' in available_cols else 'bench'
            rank_col = 'overall_rank' if 'overall_rank' in available_cols else 'rank'

            # Calculate various metrics with error handling
            try:
                if points_col in available_cols:
                    total_points = history[points_col].sum()
                    avg_points = history[points_col].mean()
                    consistency = history[points_col].std()
                    best_gw = history[points_col].max()
                    worst_gw = history[points_col].min()
                    recent_form = history.tail(5)[points_col].mean() if len(history) >= 5 else avg_points
                else:
                    total_points = avg_points = consistency = best_gw = worst_gw = recent_form = 0

                # Transfer analysis
                if transfers_col in available_cols:
                    total_transfers = history[transfers_col].sum()
                else:
                    total_transfers = 0

                if transfer_cost_col in available_cols:
                    transfer_cost = history[transfer_cost_col].sum()
                else:
                    transfer_cost = 0

                transfer_efficiency = total_transfers / transfer_cost if transfer_cost > 0 else 0

                # Bench analysis
                if bench_col in available_cols:
                    bench_points = history[bench_col].sum()
                    avg_bench = history[bench_col].mean()
                else:
                    bench_points = avg_bench = 0

                # Rank progression
                if rank_col in available_cols:
                    rank_changes = history[rank_col].diff().fillna(0)
                    biggest_rank_gain = rank_changes.min()  # Negative is good (rank improved)
                    biggest_rank_drop = rank_changes.max()  # Positive is bad (rank worsened)
                    final_overall_rank = history.iloc[-1][rank_col] if not history.empty else 0
                else:
                    biggest_rank_gain = biggest_rank_drop = final_overall_rank = 0

                stats = {
                    'entry_id': entry_id,
                    'manager_name': manager_data['player_name'],
                    'team_name': manager_data['entry_name'],
                    'final_rank': manager_data['rank'],
                    'total_points': total_points,
                    'avg_points': avg_points,
                    'consistency': consistency,
                    'best_gw': best_gw,
                    'worst_gw': worst_gw,
                    'total_transfers': total_transfers,
                    'transfer_cost': transfer_cost,
                    'transfer_efficiency': transfer_efficiency,
                    'bench_points': bench_points,
                    'avg_bench': avg_bench,
                    'recent_form': recent_form,
                    'biggest_rank_gain': biggest_rank_gain,
                    'biggest_rank_drop': biggest_rank_drop,
                    'final_overall_rank': final_overall_rank
                }

                all_stats.append(stats)

            except Exception as e:
                print(f"❌ Error calculating stats for {manager_data['player_name']}: {e}")
                continue

        return pd.DataFrame(all_stats)

# Initialize the analyzer
analyzer = FPLAnalyzer(LEAGUE_ID)

# Fetch all data
print("🚀 Starting FPL data collection...")
analyzer.fetch_bootstrap_data()
analyzer.fetch_league_standings()
analyzer.get_all_manager_histories()

# Calculate stats
league_stats = analyzer.calculate_league_stats()
advanced_stats = analyzer.calculate_advanced_stats()

# Initialize Dash app
app = dash.Dash(__name__)
app.title = f"FPL League {LEAGUE_ID} - End of Season Review"

# Custom CSS styling
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .main-container {
                background: white;
                margin: 20px;
                border-radius: 20px;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                overflow: hidden;
            }
            .header-section {
                background: linear-gradient(135deg, #37003c 0%, #e90052 100%);
                color: white;
                padding: 40px;
                text-align: center;
            }
            .stat-card {
                background: white;
                border-radius: 16px;
                padding: 24px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                border: 1px solid #e2e8f0;
                transition: all 0.3s ease;
            }
            .stat-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
            }
            .metric-value {
                font-size: 2.5rem;
                font-weight: 700;
                color: #37003c;
                margin-bottom: 8px;
            }
            .metric-label {
                font-size: 0.875rem;
                color: #64748b;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                font-weight: 500;
            }
            .section-title {
                font-size: 1.875rem;
                font-weight: 600;
                color: #1e293b;
                margin-bottom: 24px;
                text-align: center;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# Define the modern layout
app.layout = html.Div([
    html.Div([
        # Header Section
        html.Div([
            html.H1("🏆 Fantasy Premier League",
                    style={'fontSize': '3rem', 'fontWeight': '700', 'marginBottom': '8px'}),
            html.H2("End of Season Review",
                    style={'fontSize': '1.5rem', 'fontWeight': '400', 'opacity': '0.9'}),
            html.P(f"League {LEAGUE_ID} - {analyzer.league_info.get('name', 'Private League')}",
                   style={'fontSize': '1.125rem', 'marginTop': '16px', 'opacity': '0.8'}),
        ], className='header-section'),

        # Key Metrics Section
        html.Div([
            html.H2("📊 Season Overview", className='section-title'),
            html.Div([
                # Champion Card
                html.Div([
                    html.Div("🏆", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(league_stats['champion_name'], className='metric-value'),
                    html.Div("League Champion", className='metric-label'),
                    html.Div(f"{league_stats['champion_points']:,} points",
                            style={'fontSize': '1.25rem', 'fontWeight': '600', 'color': COLORS['secondary'], 'marginTop': '8px'}),
                ], className='stat-card'),

                # Total Managers
                html.Div([
                    html.Div("👥", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(f"{league_stats['total_managers']}", className='metric-value'),
                    html.Div("Total Managers", className='metric-label'),
                ], className='stat-card'),

                # Average Points
                html.Div([
                    html.Div("📈", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(f"{league_stats['average_points']:.0f}", className='metric-value'),
                    html.Div("Average Points", className='metric-label'),
                ], className='stat-card'),

                # Winning Margin
                html.Div([
                    html.Div("🎯", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                    html.Div(f"{league_stats.get('winning_margin', 0)}", className='metric-value'),
                    html.Div("Winning Margin", className='metric-label'),
                ], className='stat-card'),

            ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(250px, 1fr))',
                     'gap': '24px', 'marginBottom': '48px'}),
        ], style={'padding': '40px'}),

        # Charts Section
        html.Div([
            # Navigation Tabs
            html.Div([
                dcc.Tabs(id='main-tabs', value='overview', children=[
                    dcc.Tab(label='📊 League Overview', value='overview'),
                    dcc.Tab(label='📈 Performance Analysis', value='performance'),
                    dcc.Tab(label='👤 Manager Deep Dive', value='manager'),
                    dcc.Tab(label='🏆 Awards & Records', value='awards'),
                ], style={'marginBottom': '32px'}),
            ]),

            # Tab Content
            html.Div(id='tab-content'),

        ], style={'padding': '0 40px 40px 40px'}),

    ], className='main-container'),
], style={'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 'minHeight': '100vh', 'padding': '20px'})

# Main tab content callback
@app.callback(
    Output('tab-content', 'children'),
    Input('main-tabs', 'value')
)
def update_tab_content(active_tab):
    if active_tab == 'overview':
        return create_overview_tab()
    elif active_tab == 'performance':
        return create_performance_tab()
    elif active_tab == 'manager':
        return create_manager_tab()
    elif active_tab == 'awards':
        return create_awards_tab()
    return html.Div("Select a tab")

def create_overview_tab():
    """Create the league overview tab content"""
    # Create modern league table
    fig_table = go.Figure(data=[go.Table(
        header=dict(
            values=['Rank', 'Manager', 'Team Name', 'Total Points', 'Overall Rank'],
            fill_color=COLORS['primary'],
            font=dict(color='white', size=14, family='Inter'),
            align='center',
            height=40
        ),
        cells=dict(
            values=[
                analyzer.league_data['rank'],
                analyzer.league_data['player_name'],
                analyzer.league_data['entry_name'],
                [f"{points:,}" for points in analyzer.league_data['total']],
                [f"{rank:,}" for rank in analyzer.league_data.get('last_rank', analyzer.league_data['rank'])]
            ],
            fill_color=[['#ffffff' if i % 2 == 0 else '#f8fafc' for i in range(len(analyzer.league_data))]],
            align='center',
            font=dict(size=12, family='Inter'),
            height=35
        )
    )])

    fig_table.update_layout(
        title=dict(
            text="Final League Standings",
            font=dict(size=24, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        height=400,
        margin=dict(l=0, r=0, t=60, b=0),
        paper_bgcolor='white',
        plot_bgcolor='white'
    )

    return html.Div([
        dcc.Graph(figure=fig_table, style={'marginBottom': '32px'}),

        # Points distribution chart
        html.Div([
            dcc.Graph(id='points-distribution'),
        ])
    ])

def create_performance_tab():
    """Create the performance analysis tab"""
    return html.Div([
        html.H3("📈 Season Performance Analysis", className='section-title'),

        # Points progression chart
        html.Div([
            dcc.Graph(id='points-progression'),
        ], style={'marginBottom': '32px'}),

        # Rank progression chart
        html.Div([
            dcc.Graph(id='rank-progression'),
        ])
    ])

def create_manager_tab():
    """Create the individual manager analysis tab"""
    return html.Div([
        html.H3("👤 Individual Manager Analysis", className='section-title'),

        # Manager selector
        html.Div([
            html.Label("Select Manager:", style={'fontWeight': '600', 'marginBottom': '8px', 'display': 'block'}),
            dcc.Dropdown(
                id='manager-dropdown',
                options=[
                    {'label': f"{row['player_name']} ({row['entry_name']})", 'value': row['entry']}
                    for _, row in analyzer.league_data.iterrows()
                ],
                value=analyzer.league_data.iloc[0]['entry'],
                style={'marginBottom': '24px'}
            ),
        ]),

        # Manager analysis content
        html.Div(id='manager-analysis'),
    ])

def create_awards_tab():
    """Create the awards and records tab"""
    if advanced_stats.empty:
        return html.Div("No advanced statistics available")

    # Calculate awards
    most_consistent = advanced_stats.loc[advanced_stats['consistency'].idxmin()]
    best_form = advanced_stats.loc[advanced_stats['recent_form'].idxmax()]
    transfer_king = advanced_stats.loc[advanced_stats['total_transfers'].idxmax()]
    bench_warmer = advanced_stats.loc[advanced_stats['bench_points'].idxmax()]

    return html.Div([
        html.H3("🏆 Season Awards & Records", className='section-title'),

        # Awards grid
        html.Div([
            # Most Consistent
            html.Div([
                html.Div("🎯", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                html.Div(most_consistent['manager_name'], className='metric-value', style={'fontSize': '1.5rem'}),
                html.Div("Most Consistent", className='metric-label'),
                html.Div(f"Std Dev: {most_consistent['consistency']:.1f}",
                        style={'fontSize': '0.875rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
            ], className='stat-card'),

            # Best Recent Form
            html.Div([
                html.Div("🔥", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                html.Div(best_form['manager_name'], className='metric-value', style={'fontSize': '1.5rem'}),
                html.Div("Best Recent Form", className='metric-label'),
                html.Div(f"Last 5 GWs: {best_form['recent_form']:.1f} avg",
                        style={'fontSize': '0.875rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
            ], className='stat-card'),

            # Transfer King
            html.Div([
                html.Div("🔄", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                html.Div(transfer_king['manager_name'], className='metric-value', style={'fontSize': '1.5rem'}),
                html.Div("Transfer King", className='metric-label'),
                html.Div(f"{transfer_king['total_transfers']} transfers",
                        style={'fontSize': '0.875rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
            ], className='stat-card'),

            # Bench Points King
            html.Div([
                html.Div("🪑", style={'fontSize': '3rem', 'marginBottom': '16px'}),
                html.Div(bench_warmer['manager_name'], className='metric-value', style={'fontSize': '1.5rem'}),
                html.Div("Bench Points King", className='metric-label'),
                html.Div(f"{bench_warmer['bench_points']} points left",
                        style={'fontSize': '0.875rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
            ], className='stat-card'),

        ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(250px, 1fr))',
                 'gap': '24px', 'marginBottom': '32px'}),

        # Advanced stats table
        html.Div([
            dcc.Graph(id='advanced-stats-table'),
        ])
    ])

# Modern chart callbacks
@app.callback(
    Output('points-progression', 'figure'),
    Input('points-progression', 'id')
)
def update_points_progression(_):
    fig = go.Figure()

    # Color palette for lines
    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])
        if history is not None and not history.empty:
            # Use correct column name
            points_col = 'points' if 'points' in history.columns else 'total_points'

            if points_col in history.columns and 'event' in history.columns:
                cumulative_points = history[points_col].cumsum()

                fig.add_trace(go.Scatter(
                    x=history['event'],
                    y=cumulative_points,
                    mode='lines+markers',
                    name=manager['player_name'],
                    line=dict(width=3, color=colors[i % len(colors)]),
                    marker=dict(size=6),
                    hovertemplate=f"<b>{manager['player_name']}</b><br>" +
                                 "Gameweek: %{x}<br>" +
                                 "Total Points: %{y:,}<br>" +
                                 "<extra></extra>"
                ))

    fig.update_layout(
        title=dict(
            text="Cumulative Points Throughout Season",
            font=dict(size=20, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(
            title="Gameweek",
            gridcolor='#e2e8f0',
            showgrid=True,
            font=dict(family='Inter')
        ),
        yaxis=dict(
            title="Cumulative Points",
            gridcolor='#e2e8f0',
            showgrid=True,
            font=dict(family='Inter')
        ),
        hovermode='x unified',
        height=500,
        paper_bgcolor='white',
        plot_bgcolor='white',
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02,
            font=dict(family='Inter')
        )
    )

    return fig

@app.callback(
    Output('rank-progression', 'figure'),
    Input('rank-progression', 'id')
)
def update_rank_progression(_):
    fig = go.Figure()

    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])
        if history is not None and not history.empty and 'overall_rank' in history.columns:
            fig.add_trace(go.Scatter(
                x=history['event'],
                y=history['overall_rank'],
                mode='lines+markers',
                name=manager['player_name'],
                line=dict(width=3, color=colors[i % len(colors)]),
                marker=dict(size=6),
                hovertemplate=f"<b>{manager['player_name']}</b><br>" +
                             "Gameweek: %{x}<br>" +
                             "Overall Rank: %{y:,}<br>" +
                             "<extra></extra>"
            ))

    fig.update_layout(
        title=dict(
            text="Overall Rank Progression",
            font=dict(size=20, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(
            title="Gameweek",
            gridcolor='#e2e8f0',
            showgrid=True,
            font=dict(family='Inter')
        ),
        yaxis=dict(
            title="Overall Rank",
            autorange='reversed',  # Lower rank is better
            gridcolor='#e2e8f0',
            showgrid=True,
            font=dict(family='Inter')
        ),
        hovermode='x unified',
        height=500,
        paper_bgcolor='white',
        plot_bgcolor='white',
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02,
            font=dict(family='Inter')
        )
    )

    return fig

# Enhanced manager analysis callback
@app.callback(
    Output('manager-analysis', 'children'),
    Input('manager-dropdown', 'value')
)
def update_manager_analysis(selected_entry):
    if not selected_entry:
        return html.Div("Please select a manager")

    manager_data = analyzer.league_data[analyzer.league_data['entry'] == selected_entry].iloc[0]
    history = analyzer.manager_histories.get(selected_entry)

    if history is None or history.empty:
        return html.Div("No history data available for this manager")

    # Check available columns and use correct names
    available_cols = history.columns.tolist()
    points_col = 'points' if 'points' in available_cols else 'total_points'
    transfers_col = 'event_transfers' if 'event_transfers' in available_cols else 'transfers'
    transfer_cost_col = 'event_transfers_cost' if 'event_transfers_cost' in available_cols else 'transfer_cost'
    bench_col = 'points_on_bench' if 'points_on_bench' in available_cols else 'bench'

    # Enhanced statistics with error handling
    if points_col in available_cols:
        total_points = history[points_col].sum()
        avg_points = history[points_col].mean()
        best_gw = history.loc[history[points_col].idxmax()]
        worst_gw = history.loc[history[points_col].idxmin()]
        consistency = history[points_col].std()
        recent_form = history.tail(5)[points_col].mean() if len(history) >= 5 else avg_points
    else:
        total_points = avg_points = consistency = recent_form = 0
        best_gw = worst_gw = {'event': 0, points_col: 0}

    # Transfer analysis
    total_transfers = history[transfers_col].sum() if transfers_col in available_cols else 0
    transfer_cost = history[transfer_cost_col].sum() if transfer_cost_col in available_cols else 0

    # Bench analysis
    bench_points = history[bench_col].sum() if bench_col in available_cols else 0

    # Create enhanced performance chart
    gw_fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Gameweek Points', 'Cumulative Points'),
        vertical_spacing=0.1
    )

    # Gameweek points - use correct column
    if points_col in available_cols and 'event' in available_cols:
        gw_fig.add_trace(
            go.Scatter(
                x=history['event'],
                y=history[points_col],
                mode='lines+markers',
                name='GW Points',
                line=dict(color=COLORS['secondary'], width=3),
                marker=dict(size=6)
            ),
            row=1, col=1
        )

        # Add average line
        gw_fig.add_hline(
            y=avg_points,
            line_dash="dash",
            line_color=COLORS['danger'],
            annotation_text=f"Average: {avg_points:.1f}",
            row=1, col=1
        )

        # Cumulative points
        cumulative = history[points_col].cumsum()
        gw_fig.add_trace(
            go.Scatter(
                x=history['event'],
                y=cumulative,
                mode='lines+markers',
                name='Cumulative',
                line=dict(color=COLORS['primary'], width=3),
                marker=dict(size=6)
            ),
            row=2, col=1
        )

    gw_fig.update_layout(
        title=dict(
            text=f"Performance Analysis - {manager_data['player_name']}",
            font=dict(size=20, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        height=600,
        paper_bgcolor='white',
        plot_bgcolor='white',
        showlegend=False
    )

    gw_fig.update_xaxes(title_text="Gameweek", gridcolor='#e2e8f0', showgrid=True)
    gw_fig.update_yaxes(title_text="Points", gridcolor='#e2e8f0', showgrid=True, row=1, col=1)
    gw_fig.update_yaxes(title_text="Cumulative Points", gridcolor='#e2e8f0', showgrid=True, row=2, col=1)

    return html.Div([
        # Enhanced stats grid
        html.Div([
            html.Div([
                html.Div("📊", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{manager_data['rank']}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Final Position", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("🎯", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{total_points:,}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Total Points", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("📈", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{avg_points:.1f}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Average per GW", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("🎲", style={'fontSize': '2rem', 'marginBottom': '12px'}),
                html.Div(f"{consistency:.1f}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Consistency", className='metric-label'),
            ], className='stat-card'),

        ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(200px, 1fr))',
                 'gap': '20px', 'marginBottom': '32px'}),

        # Performance chart
        dcc.Graph(figure=gw_fig),

        # Additional insights
        html.Div([
            html.H4("🔍 Key Insights", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Ul([
                html.Li(f"Best gameweek: GW{best_gw.get('event', 'N/A')} with {best_gw.get(points_col, 0)} points"),
                html.Li(f"Worst gameweek: GW{worst_gw.get('event', 'N/A')} with {worst_gw.get(points_col, 0)} points"),
                html.Li(f"Made {total_transfers} transfers costing {transfer_cost} points"),
                html.Li(f"Left {bench_points} points on the bench"),
                html.Li(f"Recent form (last 5 GWs): {recent_form:.1f} average"),
            ], style={'color': COLORS['muted'], 'lineHeight': '1.6'})
        ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px', 'marginTop': '24px'})
    ])

# Additional callbacks for remaining charts
@app.callback(
    Output('points-distribution', 'figure'),
    Input('points-distribution', 'id')
)
def update_points_distribution(_):
    fig = go.Figure()

    # Create histogram of final points
    fig.add_trace(go.Histogram(
        x=analyzer.league_data['total'],
        nbinsx=10,
        marker_color=COLORS['secondary'],
        opacity=0.7,
        name='Points Distribution'
    ))

    # Add average line
    avg_points = analyzer.league_data['total'].mean()
    fig.add_vline(
        x=avg_points,
        line_dash="dash",
        line_color=COLORS['danger'],
        annotation_text=f"Average: {avg_points:.0f}"
    )

    fig.update_layout(
        title=dict(
            text="Final Points Distribution",
            font=dict(size=20, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(title="Total Points", font=dict(family='Inter')),
        yaxis=dict(title="Number of Managers", font=dict(family='Inter')),
        height=400,
        paper_bgcolor='white',
        plot_bgcolor='white',
        showlegend=False
    )

    return fig

@app.callback(
    Output('advanced-stats-table', 'figure'),
    Input('advanced-stats-table', 'id')
)
def update_advanced_stats_table(_):
    if advanced_stats.empty:
        return go.Figure()

    # Select key columns for display
    display_cols = ['manager_name', 'total_points', 'avg_points', 'consistency',
                   'total_transfers', 'bench_points', 'recent_form']
    display_data = advanced_stats[display_cols].round(1)

    fig = go.Figure(data=[go.Table(
        header=dict(
            values=['Manager', 'Total Points', 'Avg Points', 'Consistency',
                   'Transfers', 'Bench Points', 'Recent Form'],
            fill_color=COLORS['primary'],
            font=dict(color='white', size=14, family='Inter'),
            align='center',
            height=40
        ),
        cells=dict(
            values=[
                display_data['manager_name'],
                display_data['total_points'],
                display_data['avg_points'],
                display_data['consistency'],
                display_data['total_transfers'],
                display_data['bench_points'],
                display_data['recent_form']
            ],
            fill_color=[['#ffffff' if i % 2 == 0 else '#f8fafc' for i in range(len(display_data))]],
            align='center',
            font=dict(size=12, family='Inter'),
            height=35
        )
    )])

    fig.update_layout(
        title=dict(
            text="Advanced Statistics Summary",
            font=dict(size=20, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        height=400,
        margin=dict(l=0, r=0, t=60, b=0),
        paper_bgcolor='white',
        plot_bgcolor='white'
    )

    return fig

print("✅ Modern FPL Dashboard initialized successfully!")
print("🌐 Starting enhanced web server...")
print(f"📊 League {LEAGUE_ID} analysis complete:")
print(f"   🏆 Champion: {league_stats['champion_name']} ({league_stats['champion_points']:,} points)")
print(f"   👥 Total Managers: {league_stats['total_managers']}")
print(f"   📈 Average Points: {league_stats['average_points']:.0f}")
print(f"   🎯 Advanced Analytics: {len(advanced_stats)} manager profiles")
print("\n🌐 Open your browser and go to: http://127.0.0.1:8050")
print("🎨 Features: Modern UI, Advanced Analytics, Interactive Charts")

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=8050)
