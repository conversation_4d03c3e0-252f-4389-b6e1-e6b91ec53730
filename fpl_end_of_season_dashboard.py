"""
Fantasy Premier League End of Season Review Dashboard
Interactive web dashboard for league 623787 analysis
"""

import requests
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output, callback
import time
from datetime import datetime
import numpy as np

# Configuration
LEAGUE_ID = 623787
BOOTSTRAP_STATIC_URL = "https://fantasy.premierleague.com/api/bootstrap-static/"

class FPLAnalyzer:
    def __init__(self, league_id):
        self.league_id = league_id
        self.league_data = None
        self.gameweek_data = None
        self.manager_histories = {}
        self.bootstrap_data = None
        
    def fetch_bootstrap_data(self):
        """Fetch general FPL data including gameweeks"""
        print("📊 Fetching FPL bootstrap data...")
        response = requests.get(BOOTSTRAP_STATIC_URL)
        self.bootstrap_data = response.json()
        
        # Extract gameweek information
        events = self.bootstrap_data['events']
        self.gameweek_data = pd.DataFrame([{
            'id': event['id'],
            'name': event['name'],
            'average_score': event['average_entry_score'],
            'highest_score': event['highest_score'],
            'finished': event['finished'],
            'deadline_time': event['deadline_time']
        } for event in events if event['finished']])
        
        print(f"✅ Found {len(self.gameweek_data)} completed gameweeks")
        
    def fetch_league_standings(self):
        """Fetch league standings with pagination"""
        print(f"🏆 Fetching league {self.league_id} standings...")
        all_results = []
        page = 1
        
        while True:
            url = f"https://fantasy.premierleague.com/api/leagues-classic/{self.league_id}/standings/?page_standings={page}"
            response = requests.get(url)
            data = response.json()
            
            # Store league info from first page
            if page == 1:
                self.league_info = data['league']
                
            results = data['standings']['results']
            all_results.extend(results)
            
            if data['standings']['has_next']:
                page += 1
                time.sleep(0.5)  # Be respectful to the API
            else:
                break
                
        self.league_data = pd.DataFrame(all_results)
        print(f"✅ Found {len(self.league_data)} managers in league")
        
    def fetch_manager_history(self, entry_id):
        """Fetch gameweek history for a specific manager"""
        if entry_id in self.manager_histories:
            return self.manager_histories[entry_id]
            
        print(f"📈 Fetching history for manager {entry_id}...")
        url = f"https://fantasy.premierleague.com/api/entry/{entry_id}/history/"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            history_df = pd.DataFrame(data['current'])
            self.manager_histories[entry_id] = history_df
            time.sleep(0.3)  # Rate limiting
            return history_df
        else:
            print(f"❌ Failed to fetch history for {entry_id}")
            return pd.DataFrame()
    
    def get_all_manager_histories(self):
        """Fetch histories for all managers in the league"""
        print("📊 Fetching all manager histories...")
        for _, manager in self.league_data.iterrows():
            self.fetch_manager_history(manager['entry'])
        print("✅ All manager histories fetched")
        
    def calculate_league_stats(self):
        """Calculate comprehensive league statistics"""
        if self.league_data is None:
            return {}
            
        champion = self.league_data.iloc[0]
        runner_up = self.league_data.iloc[1] if len(self.league_data) > 1 else None
        
        stats = {
            'total_managers': len(self.league_data),
            'champion_name': champion['player_name'],
            'champion_team': champion['entry_name'],
            'champion_points': champion['total'],
            'average_points': self.league_data['total'].mean(),
            'highest_points': self.league_data['total'].max(),
            'lowest_points': self.league_data['total'].min(),
            'points_range': self.league_data['total'].max() - self.league_data['total'].min(),
        }
        
        if runner_up is not None:
            stats['winning_margin'] = champion['total'] - runner_up['total']
            stats['runner_up_name'] = runner_up['player_name']
            
        return stats

# Initialize the analyzer
analyzer = FPLAnalyzer(LEAGUE_ID)

# Fetch all data
print("🚀 Starting FPL data collection...")
analyzer.fetch_bootstrap_data()
analyzer.fetch_league_standings()
analyzer.get_all_manager_histories()

# Calculate stats
league_stats = analyzer.calculate_league_stats()

# Initialize Dash app
app = dash.Dash(__name__)
app.title = f"FPL League {LEAGUE_ID} - End of Season Review"

# Define the layout
app.layout = html.Div([
    html.Div([
        html.H1("🏆 Fantasy Premier League End of Season Review", 
                style={'textAlign': 'center', 'color': '#37003c', 'marginBottom': '10px'}),
        html.H2(f"League {LEAGUE_ID} - {analyzer.league_info.get('name', 'Private League')}", 
                style={'textAlign': 'center', 'color': '#00ff87', 'marginBottom': '30px'}),
    ]),
    
    # League Overview Cards
    html.Div([
        html.Div([
            html.H3("🥇 Champion", style={'color': '#FFD700'}),
            html.H4(league_stats['champion_name'], style={'margin': '5px 0'}),
            html.P(f"Team: {league_stats['champion_team']}", style={'margin': '2px 0'}),
            html.P(f"Points: {league_stats['champion_points']:,}", style={'margin': '2px 0', 'fontSize': '18px', 'fontWeight': 'bold'}),
            html.P(f"Winning Margin: {league_stats.get('winning_margin', 'N/A')}", style={'margin': '2px 0'}),
        ], className='overview-card', style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px', 'margin': '10px'}),
        
        html.Div([
            html.H3("📊 League Stats", style={'color': '#37003c'}),
            html.P(f"Total Managers: {league_stats['total_managers']}", style={'margin': '5px 0'}),
            html.P(f"Average Points: {league_stats['average_points']:.0f}", style={'margin': '5px 0'}),
            html.P(f"Highest Score: {league_stats['highest_points']:,}", style={'margin': '5px 0'}),
            html.P(f"Lowest Score: {league_stats['lowest_points']:,}", style={'margin': '5px 0'}),
            html.P(f"Points Range: {league_stats['points_range']:,}", style={'margin': '5px 0'}),
        ], className='overview-card', style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '10px', 'margin': '10px'}),
        
    ], style={'display': 'flex', 'justifyContent': 'space-around', 'marginBottom': '30px'}),
    
    # Final League Table
    html.Div([
        html.H3("🏆 Final League Table", style={'color': '#37003c', 'textAlign': 'center'}),
        dcc.Graph(id='league-table'),
    ], style={'marginBottom': '30px'}),
    
    # Points Progression Chart
    html.Div([
        html.H3("📈 Points Progression Throughout Season", style={'color': '#37003c', 'textAlign': 'center'}),
        dcc.Graph(id='points-progression'),
    ], style={'marginBottom': '30px'}),
    
    # Manager Analysis Section
    html.Div([
        html.H3("👤 Individual Manager Analysis", style={'color': '#37003c', 'textAlign': 'center'}),
        html.Div([
            html.Label("Select Manager:", style={'fontWeight': 'bold', 'marginRight': '10px'}),
            dcc.Dropdown(
                id='manager-dropdown',
                options=[
                    {'label': f"{row['player_name']} ({row['entry_name']})", 'value': row['entry']}
                    for _, row in analyzer.league_data.iterrows()
                ],
                value=analyzer.league_data.iloc[0]['entry'],
                style={'width': '400px', 'display': 'inline-block'}
            ),
        ], style={'textAlign': 'center', 'marginBottom': '20px'}),
        
        html.Div(id='manager-analysis'),
    ]),
])

# Callback for league table
@app.callback(
    Output('league-table', 'figure'),
    Input('league-table', 'id')
)
def update_league_table(_):
    # Create final league table
    top_10 = analyzer.league_data.head(10).copy()

    fig = go.Figure(data=[go.Table(
        header=dict(
            values=['Rank', 'Manager', 'Team Name', 'Total Points'],
            fill_color='#37003c',
            font=dict(color='white', size=14),
            align='center'
        ),
        cells=dict(
            values=[
                top_10['rank'],
                top_10['player_name'],
                top_10['entry_name'],
                [f"{points:,}" for points in top_10['total']]
            ],
            fill_color=[['#f8f9fa' if i % 2 == 0 else '#e9ecef' for i in range(len(top_10))]],
            align='center',
            font=dict(size=12)
        )
    )])

    fig.update_layout(
        title="Top 10 Final Standings",
        height=400,
        margin=dict(l=0, r=0, t=40, b=0)
    )

    return fig

# Callback for points progression
@app.callback(
    Output('points-progression', 'figure'),
    Input('points-progression', 'id')
)
def update_points_progression(_):
    fig = go.Figure()

    # Add line for each manager (top 10 for readability)
    top_managers = analyzer.league_data.head(10)

    for _, manager in top_managers.iterrows():
        history = analyzer.manager_histories.get(manager['entry'])
        if history is not None and not history.empty:
            # Calculate cumulative points
            cumulative_points = history['total_points'].cumsum()

            fig.add_trace(go.Scatter(
                x=history['event'],
                y=cumulative_points,
                mode='lines+markers',
                name=f"{manager['player_name']}",
                line=dict(width=2),
                hovertemplate=f"<b>{manager['player_name']}</b><br>" +
                             "Gameweek: %{x}<br>" +
                             "Total Points: %{y:,}<br>" +
                             "<extra></extra>"
            ))

    fig.update_layout(
        title="Cumulative Points Throughout Season (Top 10)",
        xaxis_title="Gameweek",
        yaxis_title="Cumulative Points",
        hovermode='x unified',
        height=500,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        )
    )

    return fig

# Callback for manager analysis
@app.callback(
    Output('manager-analysis', 'children'),
    Input('manager-dropdown', 'value')
)
def update_manager_analysis(selected_entry):
    if not selected_entry:
        return html.Div("Please select a manager")

    # Get manager data
    manager_data = analyzer.league_data[analyzer.league_data['entry'] == selected_entry].iloc[0]
    history = analyzer.manager_histories.get(selected_entry)

    if history is None or history.empty:
        return html.Div("No history data available for this manager")

    # Calculate statistics
    total_points = history['total_points'].sum()
    avg_points = history['total_points'].mean()
    best_gw = history.loc[history['total_points'].idxmax()]
    worst_gw = history.loc[history['total_points'].idxmin()]
    total_transfers = history['event_transfers'].sum()
    transfer_cost = history['event_transfers_cost'].sum()
    bench_points = history['points_on_bench'].sum()

    # Create gameweek performance chart
    gw_fig = go.Figure()

    gw_fig.add_trace(go.Scatter(
        x=history['event'],
        y=history['total_points'],
        mode='lines+markers',
        name='Points',
        line=dict(color='#00ff87', width=3),
        marker=dict(size=6)
    ))

    # Add average line
    gw_fig.add_hline(
        y=avg_points,
        line_dash="dash",
        line_color="red",
        annotation_text=f"Season Average: {avg_points:.1f}"
    )

    gw_fig.update_layout(
        title=f"Gameweek Performance - {manager_data['player_name']}",
        xaxis_title="Gameweek",
        yaxis_title="Points",
        height=400
    )

    return html.Div([
        # Manager stats cards
        html.Div([
            html.Div([
                html.H4("📊 Season Summary", style={'color': '#37003c'}),
                html.P(f"Final Position: {manager_data['rank']}", style={'margin': '5px 0'}),
                html.P(f"Total Points: {total_points:,}", style={'margin': '5px 0', 'fontWeight': 'bold'}),
                html.P(f"Average per GW: {avg_points:.1f}", style={'margin': '5px 0'}),
                html.P(f"League Average: {league_stats['average_points']:.0f}", style={'margin': '5px 0'}),
                html.P(f"vs League Avg: {total_points - league_stats['average_points']:+.0f}",
                       style={'margin': '5px 0', 'color': 'green' if total_points > league_stats['average_points'] else 'red'}),
            ], style={'backgroundColor': '#f8f9fa', 'padding': '15px', 'borderRadius': '8px', 'margin': '10px'}),

            html.Div([
                html.H4("🎯 Best & Worst", style={'color': '#37003c'}),
                html.P(f"Best GW: {best_gw['event']} ({best_gw['total_points']} pts)", style={'margin': '5px 0', 'color': 'green'}),
                html.P(f"Worst GW: {worst_gw['event']} ({worst_gw['total_points']} pts)", style={'margin': '5px 0', 'color': 'red'}),
                html.P(f"Total Transfers: {total_transfers}", style={'margin': '5px 0'}),
                html.P(f"Transfer Cost: -{transfer_cost} pts", style={'margin': '5px 0'}),
                html.P(f"Bench Points: {bench_points}", style={'margin': '5px 0'}),
            ], style={'backgroundColor': '#f8f9fa', 'padding': '15px', 'borderRadius': '8px', 'margin': '10px'}),
        ], style={'display': 'flex', 'justifyContent': 'space-around', 'marginBottom': '20px'}),

        # Gameweek performance chart
        dcc.Graph(figure=gw_fig),
    ])

print("✅ Dashboard initialized successfully!")
print("🌐 Starting web server...")
print(f"📊 League {LEAGUE_ID} analysis complete:")
print(f"   🏆 Champion: {league_stats['champion_name']} ({league_stats['champion_points']:,} points)")
print(f"   👥 Total Managers: {league_stats['total_managers']}")
print(f"   📈 Average Points: {league_stats['average_points']:.0f}")
print("\n🌐 Open your browser and go to: http://127.0.0.1:8050")

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=8050)
